{"_ai_translation_instructions": {"target_language": "Spanish (Latin America)", "context": "Asistente de creación de recetas de aceites esenciales multi-pasos. Usuarios proporcionan información de salud para recibir recomendaciones personalizadas de aromaterapia.", "tone": "Supportivo, alentador y profesional. Como un consultor de salud conocedor guiando al usuario.", "notes": "La terminología de salud y bienestar debe ser precisa pero accesible. Enfatice personalización y cuidado."}, "title": "Creador de Recetas de Aceites Esenciales", "subtitle": "Obtén recomendaciones personalizadas de aceites esenciales para tus preocupaciones de salud", "chatInput": {"_context": "Entrada estilo chat para preocupaciones de salud - interfaz conversacional para creación de recetas", "title": "<PERSON><PERSON><PERSON>", "description": "Cuéntanos sobre tu preocupación de salud y crearemos una receta personalizada de aceites esenciales para ti", "subtitle": "Sé lo más específico posible para las mejores recomendaciones", "placeholder": "Describe tu preocupación de salud en detalle...", "examples": {"headaches": "Tengo dolores de cabeza crónicos que empeoran con el estrés y la falta de sueño", "digestive": "Experimentando problemas digestivos con hinchazón después de las comidas", "sleep": "Dificultad para conciliar el sueño y permanecer dormido, sintiendo ansiedad a la hora de dormir", "tension": "Tensión muscular en hombros y cuello por trabajar en escritorio"}, "characterCounter": "{count}/500", "validation": {"tooShort": "Por favor, proporciona más detalles (mínimo 3 caracteres)", "tooLong": "Por favor, mantén menos de 500 caracteres"}}, "healthConcern": {"_context": "Formulario de preocupación de salud - recolección de información médica detallada para recomendaciones de aromaterapia", "title": "¿Cuál es tu preocupación de salud?", "description": "Describe tu preocupación de salud en detalle. Cuanto más específico seas, mejor podremos ayudarte a encontrar los aceites esenciales correctos.", "label": "Preocupación de Salud", "placeholder": "Por ejemplo: He estado experimentando ansiedad y estrés del trabajo, especialmente durante períodos ocupados. Tengo dificultad para dormir y me siento tenso durante el día...", "characterCounter": "{count}/500", "minCharacters": "Mínimo 3 caracteres requerido", "examples": {"title": "💡 Ejemplos de buenas preocupaciones de salud:", "headaches": "Tengo dolores de cabeza crónicos que empeoran con el estrés y la falta de sueño", "digestive": "Experimentando problemas digestivos con hinchazón después de las comidas", "sleep": "Dificultad para conciliar el sueño y permanecer dormido, sintiendo ansiedad a la hora de dormir", "tension": "Tensión muscular en hombros y cuello por trabajar en escritorio"}, "readyToContinue": "✓ Listo para continuar"}, "demographics": {"_context": "Formulario de demografía - información personal para recomendaciones personalizadas de aceites esenciales", "title": "Cuéntanos sobre ti", "description": "Esta información nos ayuda a proporcionar recomendaciones más personalizadas de aceites esenciales basadas en tu demografía.", "gender": {"label": "<PERSON><PERSON><PERSON>", "options": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Femenino"}}, "ageCategory": {"label": "Categoría de Edad", "options": {"child": {"label": "<PERSON><PERSON> (0-12)", "description": "Consideraciones pediátricas"}, "teen": {"label": "Adolescente (13-17)", "description": "Desarrollo adolescente"}, "adult": {"label": "<PERSON><PERSON> (18-64)", "description": "Población adulta general"}, "senior": {"label": "Adulto mayor (65+)", "description": "Consideraciones para adultos mayores"}}}, "specificAge": {"label": "<PERSON><PERSON> Específica: {age}", "placeholder": "Selecciona categoría", "instructions": {"withCategory": "Usa el control deslizante o ingresa tu edad directamente.", "selectFirst": "Por favor, selecciona una categoría de edad primero."}}, "readyToContinue": "✓ Listo para continuar"}, "symptoms": {"_context": "Selección de síntomas - interfaz para que los usuarios seleccionen síntomas específicos basados en las causas identificadas", "title": "¿Qué síntomas estás experimentando?", "description": "Basado en las causas seleccionadas, aquí están los síntomas potenciales. Selecciona todos los que estés experimentando actualmente.", "selectedCauses": "<PERSON><PERSON><PERSON> se<PERSON>:", "tableHeader": "<PERSON><PERSON><PERSON><PERSON>", "selectionCounter": "Selecciona 1-{count} síntomas que estés experimentando", "selected": "{selected}/{total} seleccionados", "noSymptoms": "No se encontraron síntomas potenciales. Por favor, regresa al paso de causas para generarlos.", "missingData": "Datos Re<PERSON> Faltantes", "error": {"retry": "Reintentar", "noSymptomsFound": "Síntomas no encontrados. Por favor, regresa al paso de causas para generarlos.", "noSymptomSelected": "Por favor, selecciona al menos un síntoma.", "failedToAnalyze": "Falló al analizar síntomas. Por favor, inténtalo de nuevo."}, "loading": {"aiAnalyzing": "La IA está analizando tus causas para identificar síntomas potenciales...", "loadingSymptoms": "Cargando síntomas potenciales...", "mayTakeMoment": "Esto puede tomar unos momentos mientras generamos recomendaciones personalizadas"}, "streaming": {"generating": "Generando síntomas potenciales... ({count} encontrados hasta ahora)", "reviewWhileAnalyzing": "Puedes revisar los síntomas a continuación mientras continuamos analizando"}, "status": {"ready": "✓ Listo para continuar"}}, "navigation": {"_context": "Progreso de creación de recetas y elementos de navegación", "breadcrumb": {"ariaLabel": "Progreso de creación de recetas", "navigateTo": "Navegar a {step}", "progress": "Progreso", "completed": "{completedCount} de {totalSteps} completados", "percentage": "{percentage}% completo"}, "compact": {"ariaLabel": "Navegación de creación de recetas"}, "progress": "Paso {current} de {total}", "completedProgress": "{progress} de {total} completados", "currentStepAnnouncement": "Actualmente en {title}, paso {progress} de 6", "buttons": {"previous": "Anterior", "next": "Siguient<PERSON>", "complete": "Completar"}}, "summary": {"_context": "Resumen de progreso de creación de recetas", "progress": "Progreso", "completion": "{completed} de {total} completados", "percentComplete": "{percent}% completo"}, "steps": {"_context": "Navegación y descripciones de pasos de creación de recetas", "health-concern": {"title": "Preocupación de Salud", "description": "Cuéntanos sobre tu preocupación de salud u objetivo", "placeholder": "Describe tu preocupación de salud, síntomas u objetivos de bienestar...", "examples": "Ejemplos: dolores de cabeza, est<PERSON>s, problemas de sueño, problemas de piel"}, "demographics": {"title": "Demografía", "description": "Ayúdanos a personalizar tus recomendaciones"}, "causes": {"title": "Causas <PERSON>", "description": "Selecciona lo que puede estar contribuyendo a tu preocupación", "loading": "Analizando tu preocupación de salud...", "selectMultiple": "Selecciona todos los que apliquen", "noResults": "No se encontraron causas potenciales. Intenta reformular tu preocupación de salud."}, "symptoms": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Selecciona los síntomas que estás experimentando", "loading": "Encontrando síntomas relacionados...", "selectMultiple": "Selecciona todos los que apliquen", "noResults": "No se encontraron síntomas. Revisa tus causas seleccionadas."}, "properties": {"title": "Propiedades Terapéuticas", "description": "Basado en tus selecciones, estas propiedades pueden ayudar", "loading": "Determinando propiedades terapéuticas...", "oilSuggestions": "Sugerencias de Aceites", "loadingOils": "Encontrando aceites adecuados...", "noOils": "No se encontraron aceites para esta propiedad"}, "oils": {"title": "Aceites Esenciales", "description": "Tus recomendaciones personalizadas de aceites esenciales"}}, "actions": {"_context": "Acciones rápidas del panel de progreso", "saveProgress": "Guardar Progreso", "viewPrevious": "Ver Recetas Anteriores", "startNew": "Iniciar <PERSON>"}, "wizard": {"_context": "Estado y controles del asistente de creación de recetas", "status": {"loading": "Cargando..."}, "buttons": {"retry": "Reintentar"}}, "validation": {"_context": "Mensajes de validación de formulario para proceso de creación de recetas", "healthConcern": "Por favor, describe tu preocupación de salud", "demographics": "Por favor, completa tu información demográfica", "causes": "Por favor, selecciona al menos una causa potencial", "symptoms": "Por favor, selecciona al menos un síntoma", "healthConcernRequired": "La preocupación de salud es requerida para continuar"}, "streaming": {"_context": "Mensajes de estado del análisis de IA durante la creación de recetas", "analyzing": "Analizando tu información...", "error": "El análisis falló. Por favor, inténtalo de nuevo.", "completed": "¡Análisis completado exitosamente!", "found": "encontrados", "showingLatest": "Mostrando los últimos {maxVisible} de {total}", "modal": {"symptoms": {"title": "Análisis de IA en Progreso", "description": "Analizando tus causas seleccionadas para identificar síntomas potenciales"}, "properties": {"title": "Análisis de IA en Progreso", "description": "Identificando propiedades terapéuticas para abordar tus síntomas"}}, "terminal": {"streaming": "transmisión", "potentialCausesAnalysis": "Análisis de Causas Potenciales", "potentialSymptomsAnalysis": "Análisis de Síntomas Potenciales", "therapeuticPropertiesAnalysis": "Análisis de Propiedades Terapéuticas", "essentialOilsAnalysis": "Análisis de Aceites Esenciales", "causesSubtitle": "Comprendiendo factores que pueden contribuir a tus síntomas.", "symptomsSubtitle": "Identificando síntomas que pueden manifestarse basados en las causas seleccionadas.", "propertiesSubtitle": "Encontrando propiedades terapéuticas para abordar tus síntomas.", "oilsSubtitle": "Recomendando aceites esenciales con las propiedades identificadas."}, "loading": {"analyzingDemographics": "analizando demografía...", "analyzingCauses": "analizando causas seleccionadas...", "analyzingSymptoms": "analizando síntomas...", "analyzingProperties": "analizando propiedades..."}, "progress": {"analyzingMoreCauses": "Analizando más causas potenciales...", "analyzingMoreSymptoms": "Analizando más síntomas potenciales...", "analyzingMoreProperties": "<PERSON><PERSON><PERSON><PERSON> m<PERSON> propiedades terapéuticas...", "analyzingMoreOils": "Analizando más aceites esenciales..."}, "status": {"aiProcessing": "La IA está procesando tu información para encontrar insights adicionales", "streamingComplete": "Transmisión completada", "analysisInProgress": "Análisis en progreso..."}}, "debug": {"_context": "Superposición de depuración para proceso de creación de recetas - solo uso de desarrollo", "sections": {"healthConcern": "Preocupación de Salud", "demographics": "Demografía", "causes": "<PERSON><PERSON><PERSON>", "symptoms": "<PERSON><PERSON><PERSON><PERSON>", "properties": "Propiedades", "metadata": "Metadatos"}, "buttons": {"copyAll": "Copiar Todos los Datos", "copySection": "<PERSON><PERSON><PERSON>", "toggleDebug": "Alternar Depuración", "clearData": "Limpia<PERSON>"}, "labels": {"currentStep": "Paso Actual", "completedSteps": "Pasos Completados", "totalSteps": "Total de Pasos", "lastUpdated": "Última Actualización", "loading": "Cargando", "error": "Error", "streamingStatus": {"causes": "Transmitiendo <PERSON>", "symptoms": "Transmitien<PERSON>", "properties": "Transmitiendo Propiedades", "oils": "Transmitiendo Aceites"}, "enrichmentStatus": "Estado de Enriquecimiento de Propiedades", "autoAnalyze": "Auto Analizar <PERSON>es"}}}