# i18n Implementation Guidelines

## Language Handling

### Recommended Pattern

```typescript
import { useApiLanguage } from '@/lib/i18n/utils';
import { useI18n } from '@/hooks/use-i18n';

export function MyComponent() {
  const apiLanguage = useApiLanguage();
  const { t } = useI18n();
  // ... use apiLanguage for API calls and t() for UI translations
}
```

### Why This Pattern?

1. **Efficiency**: `useApiLanguage` internally handles both getting the user's language preference and converting it to the API format in one step
2. **Clarity**: Provides the API-formatted language string directly, reducing potential formatting mistakes
3. **Consistency**: Ensures uniform language handling across all components
4. **DRY (Don't Repeat Yourself)**: Eliminates the need to manually convert user language to API format in each component
5. **Type Safety**: Returns the correct API language format ('EN_US', 'PT_BR', etc.) directly

### What to Avoid

```typescript
// ❌ Don't do this:
import { useUserLanguage, getApiLanguage } from '@/lib/i18n/utils';

const userLanguage = useUserLanguage();
const apiLanguage = getApiLanguage(userLanguage);
```

This approach is more verbose and introduces unnecessary intermediate steps.

## Translation Usage

1. Use the `useI18n` hook for UI translations:
   ```typescript
   const { t } = useI18n();
   ```

2. Use namespace-prefixed keys:
   ```typescript
   // ✅ Good
   t('create-recipe:healthConcern.title')
   t('common:buttons.continue')
   
   // ❌ Bad
   t('healthConcern.title', { ns: 'create-recipe' })
   ```

3. Pass variables as the third argument:
   ```typescript
   // ✅ Good
   t('common:status.lastSaved', undefined, { time: lastSaved })
   
   // ❌ Bad
   t('common:status.lastSaved', { time: lastSaved })
   ```

## Language Files

- Keep translations organized by namespace in `/lib/i18n/messages/{locale}/{namespace}.json`
- Use consistent key hierarchies across namespaces
- Keep translation keys readable and meaningful


4. Check test coverage for i18n
5. Maintain consistent namespace structure

