# 🌍 Global Internationalization System

A simple, AI-friendly i18n solution for your entire application.

## ✨ Features

- **App-wide**: Global i18n system for all features
- **MVP-focused**: Lightweight and easy to maintain
- **AI-friendly**: Designed for easy AI translation workflow
- **Integrates with existing language utils**: Works with your current `useUserLanguage` hook
- **Type-safe**: Full TypeScript support
- **Flexible**: Support for nested keys and variable interpolation

## 🚀 Quick Start

### 1. Use in Any Component

```tsx
import { useI18n } from '@/hooks/use-i18n';

export function MyComponent() {
  const { t, locale, isPortuguese } = useI18n();
  
  return (
    <div>
      <h1>{t('createRecipe.title', 'Essential Oil Recipe Creator')}</h1>
      <button>{t('common.buttons.continue', 'Continue')}</button>
      
      {/* With variables */}
      <p>{t('createRecipe.navigation.step', 'Step {current} of {total}', {
        current: 2,
        total: 5
      })}</p>
    </div>
  );
}
```

### 2. Component Wrapper (Alternative)

```tsx
import { T } from '@/hooks/use-i18n';

export function MyComponent() {
  return (
    <button>
      <T k="common.buttons.save" fallback="Save" />
    </button>
  );
}
```

## 📁 File Structure

```
src/
├── lib/i18n/
│   ├── index.ts              # Core i18n system
│   ├── messages/
│   │   ├── en.json          # English (base)
│   │   ├── pt.json          # Portuguese (Brazil)
│   │   └── es.json          # Spanish (Latin America)
│   └── README.md            # This file
└── hooks/
    └── use-i18n.ts          # Global i18n hook
```

## 🤖 AI Translation Workflow

### Step 1: Generate Translation Files

```bash
node scripts/generate-translation-files.js
```

### Step 2: Send to AI

Copy the generated `pt.json` or `es.json` files and send to AI with this prompt:

```
Please translate this JSON file. Replace all "TRANSLATE: " prefixes with the actual translation in [Portuguese/Spanish]. Keep the JSON structure and variable placeholders like {current}, {total} unchanged. Context: Essential oils and aromatherapy web application.
```

### Step 3: Validate & Deploy

- Review the translations
- Replace the generated files with AI-translated versions
- Test in your application

## 🔧 Supported Languages

- **English** (`en`) - Base language
- **Portuguese (Brazil)** (`pt`) - Main target audience
- **Spanish (Latin America)** (`es`) - Secondary target audience

## 🎯 Translation Keys Structure

```json
{
  "common": {
    "buttons": { "save": "Save", "cancel": "Cancel" },
    "labels": { "optional": "Optional" },
    "errors": { "general": "Something went wrong" },
    "navigation": { "home": "Home", "dashboard": "Dashboard" }
  },
  "auth": {
    "login": { "title": "Welcome Back" },
    "register": { "title": "Create Account" }
  },
  "createRecipe": {
    "title": "Essential Oil Recipe Creator",
    "steps": {
      "healthConcern": {
        "title": "Health Concern",
        "description": "Tell us about your health concern"
      }
    }
  },
  "dashboard": {
    "title": "Dashboard",
    "welcome": "Welcome back"
  }
}
```

## 🔄 Integration with Existing Language Utils

Your existing `useUserLanguage()` hook automatically works with this system:

```tsx
// Your existing hook provides the user's language
const userLanguage = useUserLanguage(); // 'PT_BR', 'EN_US', etc.

// i18n system automatically maps it
const locale = mapLanguageCode(userLanguage); // 'pt', 'en', etc.
```

## 📈 Gradual Migration Strategy

1. **Start with key components**: Begin with the most important user-facing text
2. **Use fallbacks**: Always provide English fallback text
3. **Test incrementally**: Test each component as you add translations
4. **Expand gradually**: Add more translation keys as needed

## 🛠 Example Migration

### Before:
```tsx
<button>Continue to Next Step</button>
<h1>Essential Oil Recipe Creator</h1>
```

### After:
```tsx
<button>{t('common.buttons.continue', 'Continue to Next Step')}</button>
<h1>{t('createRecipe.title', 'Essential Oil Recipe Creator')}</h1>
```

## 💡 Best Practices

1. **Always provide fallbacks**: Never leave translation keys without fallback text
2. **Use descriptive keys**: `createRecipe.steps.healthConcern.title` not `title1`
3. **Group related translations**: Keep related text together in the JSON structure
4. **Test all languages**: Always test your app in all supported languages
5. **Keep it simple**: Don't over-engineer - this is an MVP solution
6. **Use consistent naming**: Follow the established pattern for translation keys

## 🔍 Debugging

Check the browser console for translation loading errors:
```
Failed to load translations for locale: pt
```

The system will automatically fallback to English if a translation fails to load.

## 🌐 App-Wide Usage Examples

### Authentication
```tsx
// Login form
<h1>{t('auth.login.title', 'Welcome Back')}</h1>
<input placeholder={t('auth.login.emailPlaceholder', 'Enter your email')} />
```

### Dashboard
```tsx
// Dashboard header
<h1>{t('dashboard.title', 'Dashboard')}</h1>
<p>{t('dashboard.welcome', 'Welcome back')}</p>
```

### Navigation
```tsx
// Navigation menu
<nav>
  <a href="/dashboard">{t('common.navigation.dashboard', 'Dashboard')}</a>
  <a href="/profile">{t('common.navigation.profile', 'Profile')}</a>
</nav>
```

---

**Ready to internationalize your entire application! 🎉** 