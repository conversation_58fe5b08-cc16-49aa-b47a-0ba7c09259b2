# i18n Migration Guide

## Overview
This guide explains how to migrate from the old translation system to the new namespace-based i18n system.

## Key Changes
- Translations are now organized by namespaces (e.g., 'common', 'auth', 'create-recipe')
- New key format: 'namespace:key.path' (e.g., 'common:button.submit')
- Backward compatibility maintained for legacy keys
- English fallback for missing translations
- AI translation metadata support

## Directory Structure
```
src/lib/i18n/
  messages/
    en/
      common.json
      auth.json
      create-recipe.json
      dashboard.json
      homepage.json
    pt/
      ...
    es/
      ...
```

## Migration Steps

1. **Update Import**
   ```typescript
   // Old
   import { useTranslation } from '@/hooks/use-translation';
   
   // New
   import { useI18n } from '@/lib/i18n';
   ```

2. **Update Hook Usage**
   ```typescript
   // Old
   const { t } = useTranslation();
   
   // New
   const { t } = useI18n();
   ```

3. **Update Translation Keys**
   ```typescript
   // Old
   t('button.submit')
   
   // New
   t('common:button.submit')
   ```

## Examples of Migrated Components

### Demographics Form
```tsx
// Before
const options = [
  { 
    value: 'child', 
    label: t('demographics.ageCategory.child.label'), 
    description: t('demographics.ageCategory.child.description') 
  }
];

// After
const options = [
  { 
    value: 'child', 
    label: t('create-recipe:demographics.ageCategory.options.child.label'), 
    description: t('create-recipe:demographics.ageCategory.options.child.description') 
  }
];
```

### AI Streaming Modal
```tsx
// Before
const getStreamingConfig = (type: string) => {
  switch (type) {
    case 'symptoms':
      return {
        terminalTitle: t('streaming.terminal.symptomsAnalysis'),
        terminalSubtitle: t('streaming.terminal.symptomsSubtitle'),
        loadingMessage: t('streaming.loading.analyzingCauses')
      };
  }
};

// After
const getStreamingConfig = (type: string) => {
  switch (type) {
    case 'symptoms':
      return {
        terminalTitle: t('create-recipe:streaming.terminal.potentialSymptomsAnalysis'),
        terminalSubtitle: t('create-recipe:streaming.terminal.symptomsSubtitle'),
        loadingMessage: t('create-recipe:streaming.loading.analyzingCauses')
      };
  }
};
```

### Health Concern Form
```tsx
// Before
<h2 className="text-2xl font-bold">
  {t('healthConcern.title')}
</h2>
<p className="text-muted-foreground">
  {t('healthConcern.description')}
</p>

// After
<h2 className="text-2xl font-bold">
  {t('create-recipe:healthConcern.title')}
</h2>
<p className="text-muted-foreground">
  {t('create-recipe:healthConcern.description')}
</p>
```

### Health Concern Chat Input
```tsx
// Before
const examples = [
  t('chatInput.examples.headaches'),
  t('chatInput.examples.digestive'),
  t('chatInput.examples.sleep')
];

// After
const examples = [
  t('create-recipe:chatInput.examples.headaches'),
  t('create-recipe:chatInput.examples.digestive'),
  t('create-recipe:chatInput.examples.sleep')
];
```

## Variable Interpolation
```typescript
// Old
t('status.lastSaved', { time: '10:00 AM' })

// New
t('common:status.lastSaved', undefined, { time: '10:00 AM' })
```

## Testing
```typescript
// Mock translations
jest.mock('@/lib/i18n/messages/en/common.json', () => ({
  'status.saving': 'Saving...',
  'status.lastSaved': 'Last saved: {time}'
}));

// Test component
const { result } = renderHook(() => useI18n('en'));
const { t } = result.current;

expect(t('common:status.saving')).toBe('Saving...');
expect(t('common:status.lastSaved', undefined, { time: '10:00 AM' }))
  .toBe('Last saved: 10:00 AM');
```

## Best Practices
1. Group translations by feature/domain in appropriate namespaces
2. Use descriptive key paths that reflect the UI hierarchy
3. Keep translation keys consistent across languages
4. Add AI translation metadata for future improvements
5. Maintain English translations as the source of truth
6. Use TypeScript for better type safety
7. Write tests for critical translations

## Troubleshooting
- If a translation is missing, the system will:
  1. Try the current locale
  2. Fallback to English
  3. Use the provided fallback text
  4. Use the key as the last resort
- Check the browser console for warnings about missing translations
- Use the React DevTools to inspect the i18n context

## Need Help?
- Check the test files for more examples
- Review the PR history for migration patterns
- Contact the i18n team for assistance 