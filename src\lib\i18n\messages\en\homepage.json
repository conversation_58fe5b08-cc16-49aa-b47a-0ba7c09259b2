{"_ai_translation_instructions": {"target_language": "English (US)", "context": "Public homepage and marketing content. First impression for visitors and essential oil information.", "tone": "Inspiring, trustworthy, and educational. Convey expertise while remaining approachable.", "notes": "Focus on benefits and value proposition. Include essential oil safety information and interactive chat features."}, "hero": {"_context": "Main homepage hero section", "title": "Discover Personalized Essential Oil Recipes", "subtitle": "Create custom aromatherapy blends tailored to your unique health needs", "cta": "Start Creating"}, "features": {"_context": "Homepage feature highlights", "aiPowered": "AI-Powered Recommendations", "personalized": "Personalized for You", "safetyFirst": "Safety First Approach"}, "oils": {"_context": "Essential oil information - safety guidelines and therapeutic properties", "safety": {"_context": "Essential oil safety information and warnings", "title": "Safety Information", "pregnancy": "Pregnancy Safe", "children": "Child Safe", "sensitive": "Sensitive Skin Safe", "dilution": "Dilution Required", "photosensitive": "Photosensitive - Avoid Sun"}, "properties": {"_context": "Therapeutic properties of essential oils for health benefits", "antimicrobial": "Antimicrobial", "antiInflammatory": "Anti-inflammatory", "calming": "Calming", "energizing": "Energizing", "analgesic": "Pain Relief"}}, "chat": {"_context": "AI chat interface for essential oil questions and guidance", "title": "AI Chat", "placeholder": "Ask me anything about essential oils...", "send": "Send", "thinking": "Thinking...", "newChat": "New Chat", "clearHistory": "Clear History"}}