# I18n Namespace Migration Report

## Summary

This report outlines the files that need to be updated to use the new i18n namespacing convention. The goal is to migrate all translation keys to the `namespace:key` format (e.g., `create-recipe:title`) and remove the old, non-namespaced keys from the root translation files (`en.json`, `pt.json`).

This will improve the organization and maintainability of our internationalization system.

## Files to Update

The following files contain one or more instances of non-namespaced translation keys. They need to be updated to use the appropriate namespace.

### 1. `src/features/create-recipe/components/symptoms-selection.tsx`

- **Namespace:** `create-recipe`
- **Incorrect Keys:**
  - `t('symptoms.title')` should be `t('create-recipe:symptoms.title')`
  - `t('symptoms.description')` should be `t('create-recipe:symptoms.description')`
  - `t('symptoms.selectedCauses')` should be `t('create-recipe:symptoms.selectedCauses')`
  - `t('symptoms.tableHeader')` should be `t('create-recipe:symptoms.tableHeader')`
  - `t('symptoms.selectionCounter')` should be `t('create-recipe:symptoms.selectionCounter')`
  - `t('symptoms.selected')` should be `t('create-recipe:symptoms.selected')`

### 2. `src/features/create-recipe/components/recipe-navigation-buttons.tsx`

- **Namespace:** `create-recipe`
- **Incorrect Keys:**
  - `t('navigation.next')` should be `t('create-recipe:navigation.next')`
  - `t('navigation.previous')` should be `t('create-recipe:navigation.previous')`
  - `t('navigation.complete')` should be `t('create-recipe:navigation.complete')`

### 3. `src/features/create-recipe/components/health-concern-form.tsx`

- **Namespace:** `create-recipe`
- **Incorrect Keys:**
  - `t('healthConcern.title')` should be `t('create-recipe:healthConcern.title')`
  - `t('healthConcern.description')` should be `t('create-recipe:healthConcern.description')`
  - `t('healthConcern.label')` should be `t('create-recipe:healthConcern.label')`
  - `t('healthConcern.placeholder')` should be `t('create-recipe:healthConcern.placeholder')`
  - `t('healthConcern.characterCounter')` should be `t('create-recipe:healthConcern.characterCounter')`

### 4. `src/features/create-recipe/components/health-concern-chat-input.tsx`

- **Namespace:** `create-recipe`
- **Incorrect Keys:**
  - `t('chatInput.title')` should be `t('create-recipe:chatInput.title')`
  - `t('chatInput.description')` should be `t('create-recipe:chatInput.description')`
  - `t('chatInput.subtitle')` should be `t('create-recipe:chatInput.subtitle')`
  - `t('chatInput.placeholder')` should be `t('create-recipe:chatInput.placeholder')`
  - `t('chatInput.examples.headaches')` should be `t('create-recipe:chatInput.examples.headaches')`

### 5. `src/features/create-recipe/components/demographics-form.tsx`

- **Namespace:** `create-recipe`
- **Incorrect Keys:**
  - `t('demographics.title')` should be `t('create-recipe:demographics.title')`
  - `t('demographics.description')` should be `t('create-recipe:demographics.description')`
  - `t('demographics.gender.label')` should be `t('create-recipe:demographics.gender.label')`
  - `t('demographics.ageCategory.label')` should be `t('create-recipe:demographics.ageCategory.label')`

### 6. `src/features/create-recipe/components/dashboard-layout.tsx`

- **Namespace:** `create-recipe`
- **Incorrect Keys:**
  - `t('navigation.breadcrumb.progress')` should be `t('create-recipe:navigation.breadcrumb.progress')`
  - `t('navigation.breadcrumb.completed')` should be `t('create-recipe:navigation.breadcrumb.completed')`
  - `t('navigation.breadcrumb.percentage')` should be `t('create-recipe:navigation.breadcrumb.percentage')`

### 7. `src/features/create-recipe/components/breadcrumb-navigation.tsx`

- **Namespace:** `create-recipe`
- **Incorrect Keys:**
  - `t('steps.healthConcern.title')` should be `t('create-recipe:steps.healthConcern.title')`
  - `t('steps.demographics.title')` should be `t('create-recipe:steps.demographics.title')`
  - `t('steps.causes.title')` should be `t('create-recipe:steps.causes.title')`
  - `t('steps.symptoms.title')` should be `t('create-recipe:steps.symptoms.title')`
  - `t('steps.properties.title')` should be `t('create-recipe:steps.properties.title')`

### 8. `src/components/ui/ai-streaming-modal.tsx`

- **Namespace:** `create-recipe`
- **Incorrect Keys:**
  - `t('streaming.modal.symptoms.title')` should be `t('create-recipe:streaming.modal.symptoms.title')`
  - `t('streaming.modal.symptoms.description')` should be `t('create-recipe:streaming.modal.symptoms.description')`
  - `t('streaming.modal.properties.title')` should be `t('create-recipe:streaming.modal.properties.title')`
  - `t('streaming.modal.properties.description')` should be `t('create-recipe:streaming.modal.properties.description')`

## Duplicated Keys in Root Translation Files

The following keys are duplicated in the root translation files (`en.json`, `pt.json`) and should be removed, as they have been migrated to their respective namespaces.

- `steps`
- `navigation`
- `validation`
- `streaming`
- `causesSelection`
- `symptoms`
- `therapeuticProperties`
- `healthConcern`
- `demographics`
- `chatInput`

## Internal Namespace Analysis: Navigation vs. Page Content

After a deeper analysis, it's clear that the duplicated keys within the `create-recipe.json` namespace serve distinct purposes and are **not redundant**.

**Finding:**

There are two sets of translations for each step of the recipe creator:

1.  **Navigation Titles:** Short, concise titles found within the `steps` object (e.g., `steps.healthConcern.title` is "Health Concern").
2.  **Page Content Titles:** More descriptive titles found in the top-level objects (e.g., `healthConcern.title` is "What's your health concern?").

**Pattern Analysis:**

-   **Navigation components** (like breadcrumbs and step indicators) should use the concise titles from the `steps` object.
-   **Page components** (the main content of each step) should use the descriptive titles and text from the corresponding top-level object (e.g., `healthConcern`, `demographics`).

**The Problem:**

The investigation revealed that some components are using the wrong translation keys. For example, `breadcrumb-navigation.tsx` incorrectly attempts to call a non-existent `dashboard:stepTitles` namespace. This is a bug that needs to be fixed.

**Recommendation:**

The `steps` object and the top-level objects within `create-recipe.json` must both be preserved. The refactoring effort should focus on ensuring components use the correct key for their context (Navigation vs. Page Content).

**Updated Task List:**

1.  **Fix Incorrect Translation Calls:**
    -   `breadcrumb-navigation.tsx`: Correct the `t()` call to use `create-recipe:steps.{{stepName}}.title`.
    -   Review other components to ensure they follow the correct pattern.
2.  **Enforce Namespace Usage:**
    -   Update all component files listed in the "Files to Update" section to use the `create-recipe:` namespace.
3.  **Clean Up Root Files:**
    -   Remove the duplicated, non-namespaced keys from `en.json` and `pt.json`.

## Next Steps

1.  Execute the updated task list to refactor the components and clean up the translation files. 