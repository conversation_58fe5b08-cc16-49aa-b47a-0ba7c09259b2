{"_ai_translation_instructions": {"target_language": "Portuguese (Brazil)", "context": "Assistente de criação de receitas de óleos essenciais multi-etapas. Usuários fornecem informações de saúde para receber recomendações personalizadas de aromaterapia.", "tone": "<PERSON><PERSON><PERSON><PERSON>, encorajador e profissional. Como um consultor de saúde experiente guiando o usuário.", "notes": "Terminologia de saúde e bem-estar deve ser precisa mas acessível. Enfatize personalização e cuidado. Usuários podem estar em estados vulneráveis ao buscar soluções de saúde."}, "title": "Criador de Receitas de Óleos Essenciais", "subtitle": "Obtenha recomendações personalizadas de óleos essenciais para suas preocupações de saúde", "chatInput": {"_context": "Entrada estilo chat para preocupações de saúde - interface conversacional para criação de receitas", "title": "Criar <PERSON><PERSON> Receita", "description": "Conte-nos sobre sua preocupação de saúde e criaremos uma receita personalizada de óleos essenciais para você", "subtitle": "Seja o mais específico possível para as melhores recomendações", "placeholder": "Descreva sua preocupação de saúde em detalhes...", "examples": {"headaches": "Tenho dores de cabeça crônicas que pioram com estresse e falta de sono", "digestive": "Experienciando problemas digestivos com inchaço após as refeições", "sleep": "Dificuldade para dormir e permanecer dormindo, sentindo ansiedade na hora de dormir", "tension": "Tensão muscular nos ombros e pescoço por trabalhar na mesa"}, "characterCounter": "{count}/500", "validation": {"tooShort": "Por favor, forne<PERSON> mais de<PERSON> (mínimo 3 caracteres)", "tooLong": "Por favor, mantenha abaixo de 500 caracteres"}}, "healthConcern": {"_context": "Formulário de preocupação de saúde - coleta de informações médicas detalhadas para recomendações de aromaterapia", "title": "Qual é sua preocupação de saúde?", "description": "Descreva sua preocupação de saúde em detalhes. Quanto mais específico você for, melhor poderemos ajudá-lo a encontrar os óleos essenciais certos.", "label": "Preocupação de Saúde", "placeholder": "Por exemplo: Tenho experimentado ansiedade e estresse do trabalho, especialmente durante períodos ocupados. Tenho dificuldade para dormir e me sinto tenso durante o dia...", "characterCounter": "{count}/500", "minCharacters": "Mínimo 3 caracteres obrigatório", "examples": {"title": "💡 Exemplos de boas preocupações de saúde:", "headaches": "Tenho dores de cabeça crônicas que pioram com estresse e falta de sono", "digestive": "Experienciando problemas digestivos com inchaço após as refeições", "sleep": "Dificuldade para dormir e permanecer dormindo, sentindo ansiedade na hora de dormir", "tension": "Tensão muscular nos ombros e pescoço por trabalhar na mesa"}}, "demographics": {"_context": "Formulário de demografia - informações pessoais para recomendações personalizadas de óleos essenciais", "title": "Conte-nos sobre você", "description": "Essas informações nos ajudam a fornecer recomendações mais personalizadas de óleos essenciais com base em sua demografia.", "gender": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options": {"male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Feminino"}}, "ageCategory": {"label": "Categoria de Idade", "options": {"child": {"label": "Criança (0-12)", "description": "Considerações pediátricas"}, "teen": {"label": "Adolescente (13-17)", "description": "Desenvolvimento adolescente"}, "adult": {"label": "<PERSON><PERSON> (18-64)", "description": "População adulta geral"}, "senior": {"label": "<PERSON><PERSON><PERSON> (65+)", "description": "Considerações para idosos"}}}, "specificAge": {"label": "Idade Específica: {age}", "unit": "<PERSON><PERSON> de idade", "placeholder": "Selecione categoria", "instructions": {"withCategory": "Use o controle deslizante ou digite sua idade diretamente.", "selectFirst": "Por favor, selecione uma categoria de idade primeiro."}}}, "steps": {"_context": "Navegação e descrições das etapas de criação de receitas", "healthConcern": {"title": "Preocupação de Saúde", "description": "Conte-nos sobre sua preocupação de saúde ou objetivo", "placeholder": "Descreva sua preocupação de saúde, sintomas ou objetivos de bem-estar...", "examples": "Exemplos: dores de cab<PERSON>ça, estresse, problemas de sono, problemas de pele"}, "demographics": {"title": "Demografia", "description": "Ajude-nos a personalizar suas recomendações"}, "causes": {"title": "<PERSON><PERSON><PERSON>", "description": "Selecione o que pode estar contribuindo para sua preocupação", "loading": "Analisando sua preocupação de saúde...", "selectMultiple": "Selecione todos os que se aplicam", "noResults": "Nenhuma causa potencial encontrada. Tente reformular sua preocupação de saúde."}, "symptoms": {"title": "<PERSON><PERSON><PERSON>", "description": "Selecione os sintomas que você está experimentando", "loading": "Encontrando sintomas relacionados...", "selectMultiple": "Selecione todos os que se aplicam", "noResults": "Nenhum sintoma encontrado. Revise suas causas selecionadas."}, "properties": {"title": "Pro<PERSON><PERSON>ades Terapêuticas", "description": "Com base em suas seleções, essas propriedades podem ajudar", "loading": "Determinando propriedades terapêuticas...", "oilSuggestions": "Sugestões de Óleos", "loadingOils": "Encontrando óleos adequados...", "noOils": "Nenhum óleo encontrado para esta propriedade", "noProperties": "Nenhuma propriedade terapêutica encontrada. Por favor, volte à etapa de sintomas.", "analyzing": "<PERSON><PERSON><PERSON><PERSON> propriedades para encontrar os melhores óleo<PERSON> essencia<PERSON>... ({count}/{total} concluídos)", "recommendations": "Recomendações de óleos essenciais para suas propriedades terapêuticas {hasOils, select, true {prontas abaixo} other {carregando...}}", "reanalyze": "<PERSON><PERSON><PERSON><PERSON>", "next": "Continuar para Próxima Etapa", "analysisComplete": "<PERSON><PERSON><PERSON><PERSON> con<PERSON>"}, "oils": {"title": "<PERSON><PERSON><PERSON>", "description": "Suas recomendações personalizadas de óleos essenciais"}}, "actions": {"_context": "Ações rápidas do painel de progresso", "saveProgress": "<PERSON><PERSON>", "viewPrevious": "Ver Receitas Anteriores", "startNew": "Iniciar Nova Receita"}, "navigation": {"_context": "Progresso da criação de receitas e elementos de navegação", "breadcrumb": {"ariaLabel": "Progresso da criação de receitas", "navigateTo": "Navegar para {step}", "progress": "Progresso", "completed": "{completedCount} de {totalSteps} concluídos", "percentage": "{percentage}% concluído"}, "compact": {"ariaLabel": "Navegação da criação de receitas"}, "progress": "Etapa {current} de {total}", "completedProgress": "{progress} de {total} concluídas", "currentStepAnnouncement": "Atualmente na {title}, etapa {progress} de 6", "buttons": {"previous": "Anterior", "next": "Próximo", "complete": "Concluir"}}, "summary": {"_context": "Resumo do progresso da criação de receitas", "progress": "Progresso", "completion": "{completed} de {total} concluídos", "percentComplete": "{percent}% concluído"}, "wizard": {"_context": "Estado e controles do assistente de criação de receitas", "status": {"loading": "Carregando..."}, "buttons": {"retry": "Tentar novamente"}}, "validation": {"_context": "Mensagens de validação de formulário para processo de criação de receitas", "healthConcern": "Por favor, descreva sua preocupação de saúde", "demographics": "Por favor, complete suas informações demográficas", "causes": "Por favor, selecione pelo menos uma causa potencial", "symptoms": "Por favor, selecione pelo menos um sintoma", "healthConcernRequired": "Preocupação de saúde é obrigatória para continuar"}, "streaming": {"_context": "Mensagens de status da análise de IA durante a criação de receitas", "analyzing": "Analisando suas informações...", "analyzingDescription": "Estamos analisando seu perfil e problema de saúde para identificar causas potenciais e fornecer recomendações personalizadas.", "error": "<PERSON><PERSON><PERSON><PERSON> falhou. Por favor, tente novamente.", "completed": "Análise concluída com sucesso!", "found": "encontrados", "showingLatest": "Mostrando os últimos {maxVisible} de {total}", "terminal": {"streaming": "transmissão", "potentialCausesAnalysis": "Análise de Causas Potenciais", "potentialSymptomsAnalysis": "Análise de Sintomas Potenciais", "therapeuticPropertiesAnalysis": "Análise de Propriedades Terapêuticas", "essentialOilsAnalysis": "<PERSON><PERSON><PERSON><PERSON>", "causesSubtitle": "Compreendendo fatores que podem contribuir para seus sintomas.", "symptomsSubtitle": "Identificando sintomas que podem se manifestar com base nas causas selecionadas.", "propertiesSubtitle": "Encontrando propriedades terapêuticas para abordar seus sintomas.", "oilsSubtitle": "Recomendando óleos essenciais com as propriedades identificadas."}, "loading": {"analyzingDemographics": "analisando demografia...", "analyzingCauses": "analisando causas selecionadas...", "analyzingSymptoms": "analisando sintomas...", "analyzingProperties": "analisando propriedades..."}, "progress": {"analyzingMoreCauses": "<PERSON><PERSON><PERSON><PERSON> mais causas potenciais...", "analyzingMoreSymptoms": "<PERSON><PERSON><PERSON><PERSON> mais sintomas potenciais...", "analyzingMoreProperties": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> propriedades terapêuticas...", "analyzingMoreOils": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON> es<PERSON>..."}, "status": {"aiProcessing": "IA está processando suas informações para encontrar insights adicionais", "liveAnalysis": "Análise ao vivo em andamento"}}, "causesSelection": {"title": "O que pode estar causando sua preocupação de saúde?", "description": "Com base em sua preocupação de saúde, aqui estão algumas causas potenciais. Selecione todas que possam se aplicar à sua situação.", "healthConcernLabel": "Sua preocupação de saúde:", "error": {"retry": "Tentar novamente", "noPotentialCauses": "Causas potenciais não encontradas. Por favor, volte à etapa de demografia para gerá-las.", "noCauseSelected": "Por favor, selecione pelo menos uma causa potencial.", "failedToAnalyze": "Falha ao analisar sintomas. Por favor, tente novamente."}, "loading": {"aiAnalyzing": "A IA está analisando suas informações para identificar causas potenciais...", "loadingCauses": "Carregando causas potenciais...", "mayTakeMoment": "<PERSON><PERSON> pode levar alguns momentos enquanto geramos recomendações personalizadas"}, "streaming": {"generating": "Gerando causas potenciais... ({count} encontradas até agora)", "reviewWhileAnalyzing": "Você pode revisar as causas abaixo enquanto continuamos analisando"}, "selectionCounter": {"label": "Selecione 1-{count} causas que possam se aplicar a você", "selected": "{selected}/{total} selecionadas"}, "tableHeader": "<PERSON><PERSON><PERSON>", "emptyState": {"noCauses": "Nenhuma causa potencial encontrada. Por favor, volte e verifique sua preocupação de saúde."}, "status": {"ready": "✓ Pronto para continuar"}}, "symptoms": {"_context": "Seleção de sintomas - interface para usuários selecionarem sintomas específicos baseados nas causas identificadas", "title": "Quais sintomas você está experimentando?", "description": "Com base nas causas selecionadas, aqui estão os sintomas potenciais. Selecione todos que você está experimentando atualmente.", "selectedCauses": "Causas selecionadas:", "tableHeader": "<PERSON><PERSON><PERSON>", "selectionCounter": "Selecione 1-{count} sintomas que você está experimentando", "selected": "{selected}/{total} selecionados", "noSymptoms": "Nenhum sintoma potencial encontrado. Por favor, volte à etapa de causas para gerá-los.", "missingData": "Dados Obrigatórios Ausentes", "error": {"retry": "Tentar novamente", "noSymptomsFound": "Sintomas não encontrados. Por favor, volte à etapa de causas para gerá-los.", "noSymptomSelected": "Por favor, selecione pelo menos um sintoma.", "failedToAnalyze": "Falha ao analisar sintomas. Por favor, tente novamente."}, "loading": {"aiAnalyzing": "A IA está analisando suas causas para identificar sintomas potenciais...", "loadingSymptoms": "Carregando sintomas potenciais...", "mayTakeMoment": "<PERSON><PERSON> pode levar alguns momentos enquanto geramos recomendações personalizadas"}, "streaming": {"generating": "Gerando sintomas potenciais... ({count} encontrados até agora)", "reviewWhileAnalyzing": "Você pode revisar os sintomas abaixo enquanto continuamos analisando"}, "status": {"ready": "✓ Pronto para continuar"}}, "therapeuticProperties": {"_context": "Seção de propriedades terapêuticas - exibição de propriedades e óleos essenciais recomendados com informações de segurança", "mainTable": {"headers": {"therapeuticProperties": "<PERSON><PERSON><PERSON><PERSON> ({count})", "relevancy": "Relevância", "oils": "<PERSON><PERSON><PERSON> ({count})", "details": "<PERSON><PERSON><PERSON>"}}, "expandedTable": {"headers": {"essentialOil": "<PERSON><PERSON>", "match": "Correspondência", "safetyStatus": "Status de Segurança", "details": "<PERSON><PERSON><PERSON>"}}, "safetyInfo": "Informações de Segurança", "suggestedOils": "<PERSON><PERSON><PERSON>", "loading": {"enriching": "Enriquecendo óleos com dados de segurança..."}, "error": {"safety": "Ocorreu um erro ao buscar dados de segurança."}}}